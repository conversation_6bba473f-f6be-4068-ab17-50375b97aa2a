import stripe
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import asyncio
import httpx

from config import Config
from database import get_db, Payment, User, get_user_by_telegram_id

logger = logging.getLogger(__name__)

class PaymentHandler:
    def __init__(self):
        stripe.api_key = Config.STRIPE_SECRET_KEY
        self.webhook_secret = None  # Set this if using Stripe webhooks
    
    async def create_payment_intent(self, amount: float, currency: str = "USD", 
                                  user_id: int = None, subscription_type: str = "basic") -> Dict[str, Any]:
        """Create a Stripe payment intent"""
        try:
            intent = stripe.PaymentIntent.create(
                amount=int(amount * 100),  # Convert to cents
                currency=currency.lower(),
                metadata={
                    'user_id': str(user_id),
                    'subscription_type': subscription_type,
                    'platform': 'telegram_bot'
                }
            )
            
            return {
                'success': True,
                'client_secret': intent.client_secret,
                'payment_intent_id': intent.id
            }
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def confirm_payment(self, payment_intent_id: str) -> Dict[str, Any]:
        """Confirm a payment intent"""
        try:
            intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            
            if intent.status == 'succeeded':
                return {
                    'success': True,
                    'amount': intent.amount / 100,
                    'currency': intent.currency,
                    'metadata': intent.metadata
                }
            else:
                return {'success': False, 'status': intent.status}
                
        except stripe.error.StripeError as e:
            logger.error(f"Payment confirmation error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def create_subscription(self, customer_id: str, price_id: str) -> Dict[str, Any]:
        """Create a recurring subscription"""
        try:
            subscription = stripe.Subscription.create(
                customer=customer_id,
                items=[{'price': price_id}],
                payment_behavior='default_incomplete',
                payment_settings={'save_default_payment_method': 'on_subscription'},
                expand=['latest_invoice.payment_intent'],
            )
            
            return {
                'success': True,
                'subscription_id': subscription.id,
                'client_secret': subscription.latest_invoice.payment_intent.client_secret
            }
        except stripe.error.StripeError as e:
            logger.error(f"Subscription creation error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def create_customer(self, email: str, name: str, telegram_id: int) -> Dict[str, Any]:
        """Create a Stripe customer"""
        try:
            customer = stripe.Customer.create(
                email=email,
                name=name,
                metadata={'telegram_id': str(telegram_id)}
            )
            
            return {
                'success': True,
                'customer_id': customer.id
            }
        except stripe.error.StripeError as e:
            logger.error(f"Customer creation error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def handle_webhook(self, payload: str, sig_header: str) -> Dict[str, Any]:
        """Handle Stripe webhook events"""
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, self.webhook_secret
            )
            
            if event['type'] == 'payment_intent.succeeded':
                await self._handle_payment_success(event['data']['object'])
            elif event['type'] == 'payment_intent.payment_failed':
                await self._handle_payment_failure(event['data']['object'])
            elif event['type'] == 'invoice.payment_succeeded':
                await self._handle_subscription_payment(event['data']['object'])
            
            return {'success': True}
            
        except ValueError as e:
            logger.error(f"Invalid payload: {e}")
            return {'success': False, 'error': 'Invalid payload'}
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid signature: {e}")
            return {'success': False, 'error': 'Invalid signature'}
    
    async def _handle_payment_success(self, payment_intent):
        """Handle successful payment"""
        try:
            user_id = int(payment_intent['metadata'].get('user_id'))
            subscription_type = payment_intent['metadata'].get('subscription_type', 'basic')
            
            db = next(get_db())
            user = get_user_by_telegram_id(db, user_id)
            
            if user:
                # Record payment
                payment = Payment(
                    user_id=user.id,
                    amount=payment_intent['amount'] / 100,
                    currency=payment_intent['currency'],
                    transaction_id=payment_intent['id'],
                    status='completed',
                    payment_type='subscription',
                    completed_at=datetime.utcnow()
                )
                db.add(payment)
                
                # Update user subscription
                user.subscription_type = subscription_type
                user.subscription_start = datetime.utcnow()
                user.subscription_end = datetime.utcnow() + timedelta(days=30)
                user.is_active = True
                user.total_paid += payment_intent['amount'] / 100
                user.last_payment = datetime.utcnow()
                
                db.commit()
                logger.info(f"Payment processed for user {user_id}")
            
            db.close()
            
        except Exception as e:
            logger.error(f"Error handling payment success: {e}")
    
    async def _handle_payment_failure(self, payment_intent):
        """Handle failed payment"""
        try:
            user_id = int(payment_intent['metadata'].get('user_id'))
            
            db = next(get_db())
            user = get_user_by_telegram_id(db, user_id)
            
            if user:
                # Record failed payment
                payment = Payment(
                    user_id=user.id,
                    amount=payment_intent['amount'] / 100,
                    currency=payment_intent['currency'],
                    transaction_id=payment_intent['id'],
                    status='failed',
                    payment_type='subscription'
                )
                db.add(payment)
                db.commit()
                logger.info(f"Payment failed for user {user_id}")
            
            db.close()
            
        except Exception as e:
            logger.error(f"Error handling payment failure: {e}")
    
    async def _handle_subscription_payment(self, invoice):
        """Handle subscription payment"""
        try:
            customer_id = invoice['customer']
            customer = stripe.Customer.retrieve(customer_id)
            telegram_id = int(customer['metadata'].get('telegram_id'))
            
            db = next(get_db())
            user = get_user_by_telegram_id(db, telegram_id)
            
            if user:
                # Extend subscription
                if user.subscription_end and user.subscription_end > datetime.utcnow():
                    # Extend existing subscription
                    user.subscription_end += timedelta(days=30)
                else:
                    # Start new subscription period
                    user.subscription_start = datetime.utcnow()
                    user.subscription_end = datetime.utcnow() + timedelta(days=30)
                
                user.is_active = True
                user.total_paid += invoice['amount_paid'] / 100
                user.last_payment = datetime.utcnow()
                
                # Record payment
                payment = Payment(
                    user_id=user.id,
                    amount=invoice['amount_paid'] / 100,
                    currency=invoice['currency'],
                    transaction_id=invoice['payment_intent'],
                    status='completed',
                    payment_type='subscription',
                    completed_at=datetime.utcnow()
                )
                db.add(payment)
                db.commit()
                
                logger.info(f"Subscription renewed for user {telegram_id}")
            
            db.close()
            
        except Exception as e:
            logger.error(f"Error handling subscription payment: {e}")
    
    async def refund_payment(self, payment_intent_id: str, amount: Optional[float] = None) -> Dict[str, Any]:
        """Refund a payment"""
        try:
            refund_data = {'payment_intent': payment_intent_id}
            if amount:
                refund_data['amount'] = int(amount * 100)
            
            refund = stripe.Refund.create(**refund_data)
            
            return {
                'success': True,
                'refund_id': refund.id,
                'amount': refund.amount / 100,
                'status': refund.status
            }
        except stripe.error.StripeError as e:
            logger.error(f"Refund error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def cancel_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel a subscription"""
        try:
            subscription = stripe.Subscription.delete(subscription_id)
            
            return {
                'success': True,
                'subscription_id': subscription.id,
                'status': subscription.status
            }
        except stripe.error.StripeError as e:
            logger.error(f"Subscription cancellation error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def get_payment_methods(self, customer_id: str) -> Dict[str, Any]:
        """Get customer's payment methods"""
        try:
            payment_methods = stripe.PaymentMethod.list(
                customer=customer_id,
                type="card"
            )
            
            return {
                'success': True,
                'payment_methods': payment_methods.data
            }
        except stripe.error.StripeError as e:
            logger.error(f"Error fetching payment methods: {e}")
            return {'success': False, 'error': str(e)}
    
    async def validate_telegram_payment(self, payment_data: Dict[str, Any]) -> bool:
        """Validate Telegram payment data"""
        try:
            # Implement Telegram payment validation logic
            # This would involve checking the payment signature and data integrity
            required_fields = ['total_amount', 'currency', 'invoice_payload', 'telegram_payment_charge_id']
            
            for field in required_fields:
                if field not in payment_data:
                    logger.error(f"Missing required field: {field}")
                    return False
            
            # Additional validation logic can be added here
            return True
            
        except Exception as e:
            logger.error(f"Payment validation error: {e}")
            return False
    
    async def process_telegram_payment(self, payment_data: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """Process Telegram payment"""
        try:
            if not await self.validate_telegram_payment(payment_data):
                return {'success': False, 'error': 'Invalid payment data'}
            
            db = next(get_db())
            user = get_user_by_telegram_id(db, user_id)
            
            if not user:
                return {'success': False, 'error': 'User not found'}
            
            # Parse subscription type from payload
            payload_parts = payment_data['invoice_payload'].split('_')
            subscription_type = payload_parts[0] if payload_parts else 'basic'
            
            # Record payment
            payment = Payment(
                user_id=user.id,
                amount=payment_data['total_amount'] / 100,
                currency=payment_data['currency'],
                transaction_id=payment_data['telegram_payment_charge_id'],
                status='completed',
                payment_type='subscription',
                completed_at=datetime.utcnow()
            )
            db.add(payment)
            
            # Update user subscription
            user.subscription_type = subscription_type
            user.subscription_start = datetime.utcnow()
            user.subscription_end = datetime.utcnow() + timedelta(days=30)
            user.is_active = True
            user.total_paid += payment_data['total_amount'] / 100
            user.last_payment = datetime.utcnow()
            
            db.commit()
            db.close()
            
            return {
                'success': True,
                'subscription_type': subscription_type,
                'subscription_end': user.subscription_end
            }
            
        except Exception as e:
            logger.error(f"Telegram payment processing error: {e}")
            return {'success': False, 'error': str(e)}
