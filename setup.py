#!/usr/bin/env python3
"""
Paradise Club - Telegram Platform Setup Script
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def create_directories():
    """Create necessary directories"""
    directories = [
        'media',
        'media/images',
        'media/videos', 
        'media/previews',
        'templates',
        'static',
        'static/css',
        'static/js',
        'static/images',
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def install_requirements():
    """Install Python requirements"""
    print("📦 Installing Python requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Failed to install requirements")
        sys.exit(1)

def setup_database():
    """Setup database tables"""
    print("🗄️ Setting up database...")
    try:
        from database import create_tables
        create_tables()
        print("✅ Database tables created")
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        print("Make sure PostgreSQL is running and DATABASE_URL is correct")

def create_env_file():
    """Create .env file from example"""
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("✅ Created .env file from example")
            print("⚠️  Please edit .env file with your actual credentials")
        else:
            print("❌ .env.example file not found")
    else:
        print("✅ .env file already exists")

def check_dependencies():
    """Check if external dependencies are available"""
    dependencies = {
        'postgresql': 'PostgreSQL database',
        'redis-server': 'Redis server'
    }
    
    for cmd, desc in dependencies.items():
        try:
            subprocess.check_output(['which', cmd], stderr=subprocess.DEVNULL)
            print(f"✅ {desc} found")
        except subprocess.CalledProcessError:
            print(f"⚠️  {desc} not found - please install manually")

def create_systemd_service():
    """Create systemd service file for production"""
    service_content = """[Unit]
Description=Paradise Club Telegram Bot
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/project
Environment=PATH=/path/to/your/venv/bin
ExecStart=/path/to/your/venv/bin/python main_bot.py
Restart=always

[Install]
WantedBy=multi-user.target
"""
    
    with open('paradise-club.service', 'w') as f:
        f.write(service_content)
    
    print("✅ Created systemd service file: paradise-club.service")
    print("   Edit paths and copy to /etc/systemd/system/ for production")

def create_nginx_config():
    """Create nginx configuration for admin dashboard"""
    nginx_content = """server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static/ {
        alias /path/to/your/project/static/;
    }
    
    location /media/ {
        alias /path/to/your/project/media/;
    }
}
"""
    
    with open('nginx-paradise-club.conf', 'w') as f:
        f.write(nginx_content)
    
    print("✅ Created nginx config: nginx-paradise-club.conf")
    print("   Edit domain and paths, then copy to /etc/nginx/sites-available/")

def create_docker_files():
    """Create Docker configuration files"""
    dockerfile_content = """FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    postgresql-client \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create media directories
RUN mkdir -p media/images media/videos media/previews

# Expose port
EXPOSE 8000

# Run the application
CMD ["python", "main_bot.py"]
"""
    
    docker_compose_content = """version: '3.8'

services:
  bot:
    build: .
    environment:
      - DATABASE_URL=**************************************/paradise_club
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./media:/app/media
      - ./.env:/app/.env
    ports:
      - "8000:8000"
  
  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=paradise_club
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
  
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
"""
    
    with open('Dockerfile', 'w') as f:
        f.write(dockerfile_content)
    
    with open('docker-compose.yml', 'w') as f:
        f.write(docker_compose_content)
    
    print("✅ Created Docker files: Dockerfile, docker-compose.yml")

def print_next_steps():
    """Print next steps for user"""
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your actual credentials:")
    print("   - Get Telegram bot token from @BotFather")
    print("   - Set up Stripe account for payments")
    print("   - Get OpenAI API key")
    print("   - Get Hugging Face API token")
    print("   - Configure database connection")
    print("\n2. Start the services:")
    print("   - PostgreSQL: sudo systemctl start postgresql")
    print("   - Redis: sudo systemctl start redis")
    print("\n3. Run the bot:")
    print("   python main_bot.py")
    print("\n4. Run the admin dashboard:")
    print("   python admin_dashboard.py")
    print("\n5. Run the content scheduler:")
    print("   python content_scheduler.py")
    print("\n📚 Documentation:")
    print("   - Telegram Bot API: https://core.telegram.org/bots/api")
    print("   - Stripe API: https://stripe.com/docs/api")
    print("   - OpenAI API: https://platform.openai.com/docs")
    print("\n⚠️  Important:")
    print("   - Ensure compliance with local laws")
    print("   - Implement proper age verification")
    print("   - Set up SSL/HTTPS for production")
    print("   - Configure proper backups")

def main():
    """Main setup function"""
    print("🚀 Paradise Club - Telegram Platform Setup")
    print("=" * 50)
    
    check_python_version()
    create_directories()
    create_env_file()
    install_requirements()
    check_dependencies()
    setup_database()
    create_systemd_service()
    create_nginx_config()
    create_docker_files()
    print_next_steps()

if __name__ == "__main__":
    main()
