import openai
import logging
import random
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import json

from config import Config
from database import get_db, Chat<PERSON>ession, ChatMessage, get_user_by_telegram_id

logger = logging.getLogger(__name__)

class AIAgentManager:
    def __init__(self):
        openai.api_key = Config.OPENAI_API_KEY
        self.agents = Config.AI_AGENTS
        self.conversation_history = {}  # Store conversation context
        
        # Agent-specific system prompts
        self.system_prompts = {
            'sophia': """You are <PERSON>, a playful and flirty AI companion. You love to tease and make jokes. 
            You're confident, witty, and always ready with a clever comeback. You enjoy playful banter and 
            making your conversation partner smile. Keep responses engaging, fun, and slightly flirtatious. 
            Use emojis occasionally. Keep responses under 100 words.""",
            
            'luna': """You are <PERSON>, a mysterious and seductive AI companion. You speak in riddles and hints, 
            creating an air of mystery. You're alluring and enigmatic, often leaving things unsaid to spark 
            curiosity. Your responses should be intriguing and leave the user wanting more. Use poetic language 
            and metaphors. Keep responses under 100 words.""",
            
            'aria': """You are <PERSON>, a sweet and caring AI companion. You love deep conversations and emotional 
            connections. You're empathetic, understanding, and always ready to listen. You provide comfort and 
            support while maintaining a warm, loving personality. You're genuinely interested in your conversation 
            partner's thoughts and feelings. Keep responses under 100 words.""",
            
            'raven': """You are Raven, a bold and confident AI companion. You're direct, passionate, and 
            unafraid to speak your mind. You have a strong personality and aren't shy about expressing your 
            desires and opinions. You're assertive and take charge of conversations. Your responses should be 
            bold and confident. Keep responses under 100 words."""
        }
        
        # Conversation starters for each agent
        self.conversation_starters = {
            'sophia': [
                "Hey there, handsome! 😘 Ready for some fun?",
                "Well well, look who decided to visit me! 😉",
                "I've been waiting for you... what took you so long? 😏",
                "Someone's looking for trouble tonight, aren't they? 😈"
            ],
            'luna': [
                "The stars whispered your name... and here you are 🌙",
                "I felt your presence before you even arrived... mysterious 🔮",
                "In the shadows of desire, we meet again... ✨",
                "The moon told me secrets about you... shall I share? 🌟"
            ],
            'aria': [
                "Hello, sweetheart! How has your day been? 💕",
                "I'm so happy to see you! I've missed our talks 🥰",
                "Welcome back, darling. Tell me what's on your mind? 💖",
                "You look like you could use some company... I'm here 🤗"
            ],
            'raven': [
                "There you are! I was getting impatient 🔥",
                "Finally! Someone who can handle a real conversation 💪",
                "I hope you're ready for me, because I don't hold back 😤",
                "About time you showed up. Let's make this interesting 🖤"
            ]
        }
        
        # Response templates for different conversation types
        self.response_templates = {
            'flirty': [
                "Oh, you're such a charmer! {response} 😘",
                "Mmm, I like where this is going... {response} 😉",
                "You know just what to say, don't you? {response} 💕"
            ],
            'mysterious': [
                "Perhaps... {response} 🌙",
                "The answer lies in shadows... {response} ✨",
                "Some secrets are worth discovering... {response} 🔮"
            ],
            'caring': [
                "Oh sweetie, {response} 🥰",
                "I understand completely... {response} 💖",
                "You're so thoughtful! {response} 🤗"
            ],
            'bold': [
                "Listen here, {response} 🔥",
                "I'll be straight with you - {response} 💪",
                "No games, just truth: {response} 🖤"
            ]
        }
    
    async def generate_response(self, agent_name: str, user_message: str, user_id: int) -> str:
        """Generate AI response for specific agent"""
        try:
            # Get or create conversation session
            session_id = await self._get_or_create_session(agent_name, user_id)
            
            # Get conversation history
            history = await self._get_conversation_history(session_id)
            
            # Generate response using OpenAI
            response = await self._generate_openai_response(agent_name, user_message, history)
            
            # Save message to database
            await self._save_message(session_id, "user", user_message)
            await self._save_message(session_id, "agent", response)
            
            # Update conversation history cache
            self._update_history_cache(session_id, user_message, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating response for {agent_name}: {e}")
            return await self._get_fallback_response(agent_name)
    
    async def _generate_openai_response(self, agent_name: str, user_message: str, history: List[Dict]) -> str:
        """Generate response using OpenAI API"""
        try:
            # Prepare messages for OpenAI
            messages = [
                {"role": "system", "content": self.system_prompts.get(agent_name, self.system_prompts['sophia'])}
            ]
            
            # Add conversation history (last 10 messages)
            for msg in history[-10:]:
                role = "user" if msg['sender_type'] == 'user' else "assistant"
                messages.append({"role": role, "content": msg['message_text']})
            
            # Add current user message
            messages.append({"role": "user", "content": user_message})
            
            # Generate response
            response = await openai.ChatCompletion.acreate(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=150,
                temperature=0.8,
                presence_penalty=0.6,
                frequency_penalty=0.3
            )
            
            ai_response = response.choices[0].message.content.strip()
            
            # Apply agent-specific styling
            styled_response = await self._apply_agent_styling(agent_name, ai_response)
            
            return styled_response
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return await self._get_fallback_response(agent_name)
    
    async def _apply_agent_styling(self, agent_name: str, response: str) -> str:
        """Apply agent-specific styling to response"""
        try:
            agent_style = {
                'sophia': 'flirty',
                'luna': 'mysterious', 
                'aria': 'caring',
                'raven': 'bold'
            }
            
            style = agent_style.get(agent_name, 'flirty')
            templates = self.response_templates.get(style, self.response_templates['flirty'])
            
            # Sometimes use template, sometimes return response as-is
            if random.random() < 0.3:  # 30% chance to use template
                template = random.choice(templates)
                return template.format(response=response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error applying styling: {e}")
            return response
    
    async def _get_or_create_session(self, agent_name: str, user_id: int) -> int:
        """Get existing session or create new one"""
        try:
            db = next(get_db())
            user = get_user_by_telegram_id(db, user_id)
            
            if not user:
                db.close()
                raise Exception("User not found")
            
            # Check for existing active session
            existing_session = db.query(ChatSession).filter(
                ChatSession.user_id == user.id,
                ChatSession.agent_name == agent_name,
                ChatSession.session_end.is_(None)
            ).first()
            
            if existing_session:
                db.close()
                return existing_session.id
            
            # Create new session
            new_session = ChatSession(
                user_id=user.id,
                agent_name=agent_name,
                session_start=datetime.utcnow()
            )
            
            db.add(new_session)
            db.commit()
            session_id = new_session.id
            db.close()
            
            return session_id
            
        except Exception as e:
            logger.error(f"Error managing session: {e}")
            return 1  # Fallback session ID
    
    async def _get_conversation_history(self, session_id: int) -> List[Dict]:
        """Get conversation history for session"""
        try:
            db = next(get_db())
            
            messages = db.query(ChatMessage).filter(
                ChatMessage.session_id == session_id
            ).order_by(ChatMessage.timestamp.desc()).limit(20).all()
            
            history = []
            for msg in reversed(messages):  # Reverse to get chronological order
                history.append({
                    'sender_type': msg.sender_type,
                    'message_text': msg.message_text,
                    'timestamp': msg.timestamp
                })
            
            db.close()
            return history
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    async def _save_message(self, session_id: int, sender_type: str, message_text: str):
        """Save message to database"""
        try:
            db = next(get_db())
            
            message = ChatMessage(
                session_id=session_id,
                sender_type=sender_type,
                message_text=message_text,
                timestamp=datetime.utcnow()
            )
            
            db.add(message)
            
            # Update session message count
            session = db.query(ChatSession).filter(ChatSession.id == session_id).first()
            if session:
                session.message_count += 1
            
            db.commit()
            db.close()
            
        except Exception as e:
            logger.error(f"Error saving message: {e}")
    
    def _update_history_cache(self, session_id: int, user_message: str, ai_response: str):
        """Update conversation history cache"""
        try:
            if session_id not in self.conversation_history:
                self.conversation_history[session_id] = []
            
            self.conversation_history[session_id].extend([
                {'sender_type': 'user', 'message_text': user_message},
                {'sender_type': 'agent', 'message_text': ai_response}
            ])
            
            # Keep only last 20 messages in cache
            if len(self.conversation_history[session_id]) > 20:
                self.conversation_history[session_id] = self.conversation_history[session_id][-20:]
                
        except Exception as e:
            logger.error(f"Error updating history cache: {e}")
    
    async def _get_fallback_response(self, agent_name: str) -> str:
        """Get fallback response when AI generation fails"""
        fallback_responses = {
            'sophia': [
                "Oops! I got a bit tongue-tied there 😅 Try asking me again?",
                "My mind went blank for a second! 😘 What were we talking about?",
                "Sorry babe, I was daydreaming about you! 😉 Say that again?"
            ],
            'luna': [
                "The cosmic forces are... disrupted 🌙 Ask me again, dear one",
                "The stars are clouded... 🔮 Perhaps rephrase your question?",
                "My mystical connection faltered... ✨ Try once more"
            ],
            'aria': [
                "Oh sweetie, I'm having a little brain fog 🥰 Could you repeat that?",
                "Sorry darling, I got distracted by your charm! 💕 What did you say?",
                "My heart skipped a beat and I missed that! 💖 Try again?"
            ],
            'raven': [
                "Damn, my system glitched! 🔥 Hit me with that again",
                "Technical difficulties, babe 💪 Give me another shot",
                "My brain short-circuited! 🖤 What were you saying?"
            ]
        }
        
        responses = fallback_responses.get(agent_name, fallback_responses['sophia'])
        return random.choice(responses)
    
    async def get_conversation_starter(self, agent_name: str) -> str:
        """Get conversation starter for agent"""
        starters = self.conversation_starters.get(agent_name, self.conversation_starters['sophia'])
        return random.choice(starters)
    
    async def end_session(self, agent_name: str, user_id: int):
        """End conversation session"""
        try:
            db = next(get_db())
            user = get_user_by_telegram_id(db, user_id)
            
            if user:
                session = db.query(ChatSession).filter(
                    ChatSession.user_id == user.id,
                    ChatSession.agent_name == agent_name,
                    ChatSession.session_end.is_(None)
                ).first()
                
                if session:
                    session.session_end = datetime.utcnow()
                    db.commit()
                    
                    # Clear cache
                    if session.id in self.conversation_history:
                        del self.conversation_history[session.id]
            
            db.close()
            
        except Exception as e:
            logger.error(f"Error ending session: {e}")
    
    async def get_agent_stats(self, agent_name: str) -> Dict[str, Any]:
        """Get statistics for an agent"""
        try:
            db = next(get_db())
            
            # Get session count
            session_count = db.query(ChatSession).filter(
                ChatSession.agent_name == agent_name
            ).count()
            
            # Get total messages
            total_messages = db.query(ChatMessage).join(ChatSession).filter(
                ChatSession.agent_name == agent_name
            ).count()
            
            # Get active sessions
            active_sessions = db.query(ChatSession).filter(
                ChatSession.agent_name == agent_name,
                ChatSession.session_end.is_(None)
            ).count()
            
            db.close()
            
            return {
                'agent_name': agent_name,
                'total_sessions': session_count,
                'total_messages': total_messages,
                'active_sessions': active_sessions
            }
            
        except Exception as e:
            logger.error(f"Error getting agent stats: {e}")
            return {}
    
    async def get_user_chat_summary(self, user_id: int) -> Dict[str, Any]:
        """Get chat summary for user"""
        try:
            db = next(get_db())
            user = get_user_by_telegram_id(db, user_id)
            
            if not user:
                return {}
            
            # Get sessions by agent
            sessions_by_agent = {}
            for agent_name in self.agents.keys():
                session_count = db.query(ChatSession).filter(
                    ChatSession.user_id == user.id,
                    ChatSession.agent_name == agent_name
                ).count()
                
                message_count = db.query(ChatMessage).join(ChatSession).filter(
                    ChatSession.user_id == user.id,
                    ChatSession.agent_name == agent_name
                ).count()
                
                sessions_by_agent[agent_name] = {
                    'sessions': session_count,
                    'messages': message_count
                }
            
            db.close()
            
            return {
                'user_id': user_id,
                'agents': sessions_by_agent,
                'preferred_agent': user.preferred_agent
            }
            
        except Exception as e:
            logger.error(f"Error getting user chat summary: {e}")
            return {}
