from fastapi import <PERSON>AP<PERSON>, Depends, HTTPException, Request, Form
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from datetime import datetime, timedelta
import json
from typing import List, Dict, Any

from config import Config
from database import get_db, User, Payment, ChatSession, GeneratedContent, ContentRequest
from content_generator import ContentGenerator
from ai_agents import AIAgentManager

app = FastAPI(title="Paradise Club Admin Dashboard")

# Setup templates and static files
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory="static"), name="static")

# Initialize services
content_generator = ContentGenerator()
ai_manager = AIAgentManager()

@app.get("/", response_class=HTMLResponse)
async def dashboard_home(request: Request, db: Session = Depends(get_db)):
    """Main dashboard page"""
    
    # Get key metrics
    total_users = db.query(User).count()
    active_subscribers = db.query(User).filter(
        User.is_active == True,
        User.subscription_end > datetime.utcnow()
    ).count()
    
    total_revenue = db.query(func.sum(Payment.amount)).filter(
        Payment.status == 'completed'
    ).scalar() or 0
    
    monthly_revenue = db.query(func.sum(Payment.amount)).filter(
        Payment.status == 'completed',
        Payment.completed_at >= datetime.utcnow() - timedelta(days=30)
    ).scalar() or 0
    
    # Get recent activity
    recent_users = db.query(User).order_by(desc(User.created_at)).limit(5).all()
    recent_payments = db.query(Payment).order_by(desc(Payment.completed_at)).limit(5).all()
    
    # Get content stats
    total_content = db.query(GeneratedContent).count()
    posted_content = db.query(GeneratedContent).filter(GeneratedContent.is_posted == True).count()
    
    context = {
        "request": request,
        "total_users": total_users,
        "active_subscribers": active_subscribers,
        "total_revenue": round(total_revenue, 2),
        "monthly_revenue": round(monthly_revenue, 2),
        "recent_users": recent_users,
        "recent_payments": recent_payments,
        "total_content": total_content,
        "posted_content": posted_content,
        "subscription_rate": round((active_subscribers / total_users * 100) if total_users > 0 else 0, 1)
    }
    
    return templates.TemplateResponse("dashboard.html", context)

@app.get("/users", response_class=HTMLResponse)
async def users_page(request: Request, db: Session = Depends(get_db)):
    """Users management page"""
    
    users = db.query(User).order_by(desc(User.created_at)).all()
    
    # Calculate user stats
    for user in users:
        user.total_sessions = db.query(ChatSession).filter(ChatSession.user_id == user.id).count()
        user.total_messages = db.query(ChatSession).filter(ChatSession.user_id == user.id).with_entities(func.sum(ChatSession.message_count)).scalar() or 0
        user.days_subscribed = (datetime.utcnow() - user.subscription_start).days if user.subscription_start else 0
    
    context = {
        "request": request,
        "users": users
    }
    
    return templates.TemplateResponse("users.html", context)

@app.get("/analytics", response_class=HTMLResponse)
async def analytics_page(request: Request, db: Session = Depends(get_db)):
    """Analytics and reports page"""
    
    # Revenue analytics
    revenue_data = []
    for i in range(30):
        date = datetime.utcnow() - timedelta(days=i)
        daily_revenue = db.query(func.sum(Payment.amount)).filter(
            Payment.status == 'completed',
            func.date(Payment.completed_at) == date.date()
        ).scalar() or 0
        
        revenue_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'revenue': float(daily_revenue)
        })
    
    revenue_data.reverse()
    
    # User growth analytics
    user_growth_data = []
    for i in range(30):
        date = datetime.utcnow() - timedelta(days=i)
        daily_signups = db.query(User).filter(
            func.date(User.created_at) == date.date()
        ).count()
        
        user_growth_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'signups': daily_signups
        })
    
    user_growth_data.reverse()
    
    # Agent popularity
    agent_stats = []
    for agent_name in Config.AI_AGENTS.keys():
        stats = await ai_manager.get_agent_stats(agent_name)
        agent_stats.append(stats)
    
    # Subscription type breakdown
    subscription_breakdown = db.query(
        User.subscription_type,
        func.count(User.id).label('count')
    ).filter(User.is_active == True).group_by(User.subscription_type).all()
    
    context = {
        "request": request,
        "revenue_data": json.dumps(revenue_data),
        "user_growth_data": json.dumps(user_growth_data),
        "agent_stats": agent_stats,
        "subscription_breakdown": subscription_breakdown
    }
    
    return templates.TemplateResponse("analytics.html", context)

@app.get("/content", response_class=HTMLResponse)
async def content_page(request: Request, db: Session = Depends(get_db)):
    """Content management page"""
    
    # Get generated content
    content_items = db.query(GeneratedContent).order_by(desc(GeneratedContent.created_at)).limit(50).all()
    
    # Get content requests
    content_requests = db.query(ContentRequest).order_by(desc(ContentRequest.created_at)).limit(20).all()
    
    context = {
        "request": request,
        "content_items": content_items,
        "content_requests": content_requests
    }
    
    return templates.TemplateResponse("content.html", context)

@app.post("/content/generate")
async def generate_content_api(
    content_type: str = Form(...),
    prompt: str = Form(...),
    style: str = Form(default="artistic"),
    db: Session = Depends(get_db)
):
    """API endpoint to generate content"""
    
    try:
        if content_type == "image":
            file_path = await content_generator.generate_image_huggingface(prompt, style)
        elif content_type == "video":
            file_path = await content_generator.generate_video_replicate(prompt)
        else:
            raise HTTPException(status_code=400, detail="Invalid content type")
        
        if file_path:
            return JSONResponse({
                "success": True,
                "file_path": file_path,
                "message": f"{content_type.title()} generated successfully"
            })
        else:
            return JSONResponse({
                "success": False,
                "message": f"Failed to generate {content_type}"
            })
    
    except Exception as e:
        return JSONResponse({
            "success": False,
            "message": f"Error: {str(e)}"
        })

@app.post("/content/post/{content_id}")
async def post_content_to_group(content_id: int, db: Session = Depends(get_db)):
    """Post content to Telegram group"""
    
    try:
        content = db.query(GeneratedContent).filter(GeneratedContent.id == content_id).first()
        
        if not content:
            raise HTTPException(status_code=404, detail="Content not found")
        
        # Here you would integrate with your Telegram bot to post content
        # For now, we'll just mark it as posted
        content.is_posted = True
        content.posted_at = datetime.utcnow()
        db.commit()
        
        return JSONResponse({
            "success": True,
            "message": "Content posted to group successfully"
        })
    
    except Exception as e:
        return JSONResponse({
            "success": False,
            "message": f"Error posting content: {str(e)}"
        })

@app.get("/api/stats")
async def get_stats_api(db: Session = Depends(get_db)):
    """API endpoint for dashboard stats"""
    
    # Real-time stats
    stats = {
        "total_users": db.query(User).count(),
        "active_subscribers": db.query(User).filter(
            User.is_active == True,
            User.subscription_end > datetime.utcnow()
        ).count(),
        "total_revenue": float(db.query(func.sum(Payment.amount)).filter(
            Payment.status == 'completed'
        ).scalar() or 0),
        "monthly_revenue": float(db.query(func.sum(Payment.amount)).filter(
            Payment.status == 'completed',
            Payment.completed_at >= datetime.utcnow() - timedelta(days=30)
        ).scalar() or 0),
        "total_content": db.query(GeneratedContent).count(),
        "active_sessions": db.query(ChatSession).filter(
            ChatSession.session_end.is_(None)
        ).count()
    }
    
    return JSONResponse(stats)

@app.post("/api/users/{user_id}/subscription")
async def update_user_subscription(
    user_id: int,
    subscription_type: str = Form(...),
    duration_days: int = Form(default=30),
    db: Session = Depends(get_db)
):
    """Update user subscription"""
    
    try:
        user = db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        user.subscription_type = subscription_type
        user.subscription_start = datetime.utcnow()
        user.subscription_end = datetime.utcnow() + timedelta(days=duration_days)
        user.is_active = True
        
        db.commit()
        
        return JSONResponse({
            "success": True,
            "message": "Subscription updated successfully"
        })
    
    except Exception as e:
        return JSONResponse({
            "success": False,
            "message": f"Error updating subscription: {str(e)}"
        })

@app.delete("/api/users/{user_id}")
async def delete_user(user_id: int, db: Session = Depends(get_db)):
    """Delete user account"""
    
    try:
        user = db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Delete related records
        db.query(ChatSession).filter(ChatSession.user_id == user_id).delete()
        db.query(Payment).filter(Payment.user_id == user_id).delete()
        db.query(ContentRequest).filter(ContentRequest.user_id == user_id).delete()
        
        # Delete user
        db.delete(user)
        db.commit()
        
        return JSONResponse({
            "success": True,
            "message": "User deleted successfully"
        })
    
    except Exception as e:
        return JSONResponse({
            "success": False,
            "message": f"Error deleting user: {str(e)}"
        })

@app.get("/api/revenue/chart")
async def get_revenue_chart_data(days: int = 30, db: Session = Depends(get_db)):
    """Get revenue chart data"""
    
    data = []
    for i in range(days):
        date = datetime.utcnow() - timedelta(days=i)
        daily_revenue = db.query(func.sum(Payment.amount)).filter(
            Payment.status == 'completed',
            func.date(Payment.completed_at) == date.date()
        ).scalar() or 0
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'revenue': float(daily_revenue)
        })
    
    data.reverse()
    return JSONResponse(data)

@app.get("/api/users/chart")
async def get_user_growth_chart_data(days: int = 30, db: Session = Depends(get_db)):
    """Get user growth chart data"""
    
    data = []
    total_users = 0
    
    for i in range(days):
        date = datetime.utcnow() - timedelta(days=days-1-i)
        daily_signups = db.query(User).filter(
            func.date(User.created_at) == date.date()
        ).count()
        
        total_users += daily_signups
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'signups': daily_signups,
            'total': total_users
        })
    
    return JSONResponse(data)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
