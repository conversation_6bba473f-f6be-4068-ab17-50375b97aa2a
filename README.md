# Paradise Club - Telegram Platform

A comprehensive Telegram-based entertainment platform with AI-powered content generation, multi-agent chat system, automated payments, and premium group management.

## 🌟 Features

### Core Platform
- **Telegram Bot Integration** - Seamless user onboarding and interaction
- **Payment Processing** - Stripe & Telegram Payments API integration
- **Subscription Management** - Basic and Premium tiers with automated access control
- **Group Management** - Automated invite system for paid subscribers

### AI-Powered Content
- **Image Generation** - AI-generated exclusive content using Stable Diffusion
- **Video Generation** - Automated video creation with Replicate API
- **Content Scheduling** - Automated daily content posting to groups
- **Custom Requests** - Personalized content generation on demand

### Multi-Agent Chat System
- **4 AI Companions** - <PERSON> (Flirty), <PERSON> (Mysterious), <PERSON> (Caring), <PERSON> (Bold)
- **Personality-Driven Conversations** - Each agent has unique characteristics
- **Context-Aware Responses** - Maintains conversation history and context
- **OpenAI GPT Integration** - Natural, engaging conversations

### Admin Dashboard
- **User Management** - View and manage all subscribers
- **Analytics & Reports** - Revenue tracking, user growth, engagement metrics
- **Content Management** - Upload, schedule, and manage content
- **Real-time Monitoring** - Live platform statistics and health monitoring

### Compliance & Safety
- **Age Verification** - Multiple verification methods (ID, Credit Card, Phone)
- **Content Moderation** - Automated filtering and safety checks
- **Privacy Protection** - Secure data handling and user privacy
- **Legal Compliance** - Built-in compliance tools and reporting

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- PostgreSQL
- Redis
- Telegram Bot Token
- Stripe Account
- OpenAI API Key
- Hugging Face API Token

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd paradise-club
```

2. **Run setup script**
```bash
python setup.py
```

3. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your credentials
```

4. **Start the services**
```bash
# Start the main bot
python main_bot.py

# Start admin dashboard (new terminal)
python admin_dashboard.py

# Start content scheduler (new terminal)
python content_scheduler.py
```

## 📋 Configuration

### Required Credentials

#### Telegram Bot Setup
1. Message @BotFather on Telegram
2. Create new bot with `/newbot`
3. Get bot token and add to `.env`
4. Create private group and add bot as admin
5. Get group ID and add to `.env`

#### Payment Setup (Stripe)
1. Create Stripe account
2. Get API keys from dashboard
3. Set up webhook endpoints
4. Configure payment methods

#### AI Services
1. **OpenAI**: Get API key from platform.openai.com
2. **Hugging Face**: Get token from huggingface.co
3. **Replicate**: Get token from replicate.com (optional for video)

### Environment Variables
```bash
# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_GROUP_ID=-*************

# Payments
STRIPE_SECRET_KEY=sk_test_...
PAYMENT_PROVIDER_TOKEN=telegram_payment_token

# AI
OPENAI_API_KEY=sk-...
HUGGINGFACE_API_TOKEN=hf_...

# Database
DATABASE_URL=postgresql://user:pass@localhost/db
REDIS_URL=redis://localhost:6379
```

## 🏗️ Architecture

### Core Components
- **main_bot.py** - Main Telegram bot handler
- **payment_handler.py** - Payment processing and subscriptions
- **content_generator.py** - AI content generation pipeline
- **ai_agents.py** - Multi-agent chat system
- **admin_dashboard.py** - Web-based admin interface
- **content_scheduler.py** - Automated content posting
- **compliance_manager.py** - Safety and compliance tools

### Database Schema
- **Users** - User accounts and subscription info
- **Payments** - Payment history and transactions
- **ChatSessions** - AI conversation tracking
- **GeneratedContent** - Content library and metadata
- **ContentRequests** - Custom content requests

## 💰 Revenue Model

### Subscription Tiers
- **Basic ($9.99/month)** - Access to group, daily content, AI chat
- **Premium ($29.99/month)** - Priority support, exclusive content, custom requests
- **Custom Requests ($15.99 each)** - Personalized content generation

### Payment Processing
- Telegram Payments API for seamless in-app purchases
- Stripe integration for recurring subscriptions
- Automated access control and subscription management

## 🤖 AI Agents

### Sophia - The Flirty Companion
- Playful and teasing personality
- Loves jokes and witty banter
- Confident and charming responses

### Luna - The Mysterious Seductress
- Speaks in riddles and metaphors
- Creates intrigue and curiosity
- Poetic and enigmatic communication

### Aria - The Caring Sweetheart
- Empathetic and understanding
- Loves deep conversations
- Provides emotional support

### Raven - The Bold Confident
- Direct and assertive
- Passionate and unafraid
- Takes charge of conversations

## 📊 Analytics & Monitoring

### Key Metrics
- User acquisition and retention
- Revenue tracking and forecasting
- Content engagement analytics
- AI conversation quality metrics

### Admin Dashboard Features
- Real-time user statistics
- Revenue and payment tracking
- Content performance analytics
- User engagement monitoring

## 🔒 Security & Compliance

### Age Verification
- ID document verification
- Credit card age validation
- Phone number verification
- Manual review process

### Content Safety
- Automated content filtering
- Blocked word detection
- Image moderation (extensible)
- User reporting system

### Data Protection
- Encrypted data storage
- GDPR compliance tools
- User privacy controls
- Secure payment processing

## 🚀 Deployment

### Development
```bash
python main_bot.py
```

### Production (Docker)
```bash
docker-compose up -d
```

### Production (Systemd)
```bash
sudo cp paradise-club.service /etc/systemd/system/
sudo systemctl enable paradise-club
sudo systemctl start paradise-club
```

## 📈 Scaling

### Performance Optimization
- Redis caching for chat sessions
- Database connection pooling
- Async/await for concurrent operations
- Content CDN for media delivery

### Infrastructure Scaling
- Horizontal scaling with load balancers
- Database read replicas
- Separate content generation workers
- Microservices architecture

## 🛠️ Development

### Adding New AI Agents
1. Add agent config to `config.py`
2. Create personality prompt in `ai_agents.py`
3. Add conversation starters and templates
4. Update bot menu options

### Custom Content Generators
1. Extend `ContentGenerator` class
2. Add new generation methods
3. Update content type handling
4. Configure API integrations

### Payment Providers
1. Implement new payment handler
2. Add provider-specific logic
3. Update webhook handling
4. Test payment flows

## 📞 Support

### Common Issues
- **Bot not responding**: Check token and permissions
- **Payment failures**: Verify Stripe configuration
- **Content generation errors**: Check API keys and quotas
- **Database errors**: Ensure PostgreSQL is running

### Logs and Debugging
- Application logs in `logs/` directory
- Enable debug mode in development
- Monitor API rate limits
- Check webhook delivery status

## ⚖️ Legal Considerations

### Compliance Requirements
- Age verification for adult content
- Payment processor terms compliance
- Local laws and regulations
- Platform terms of service

### Content Guidelines
- No illegal content
- Age-appropriate verification
- User consent and privacy
- Content moderation policies

## 🔄 Updates and Maintenance

### Regular Tasks
- Update AI model versions
- Monitor API usage and costs
- Review content moderation logs
- Update security configurations

### Backup Strategy
- Daily database backups
- Media file synchronization
- Configuration backup
- Disaster recovery plan

---

## 📄 License

This project is for educational and demonstration purposes. Ensure compliance with all applicable laws and platform terms of service before commercial use.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Submit pull request

---

**⚠️ Important**: This platform handles adult content and payments. Ensure full compliance with local laws, payment processor requirements, and platform policies before deployment.
