import logging
import asyncio
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, LabeledPrice
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, PreCheckoutQueryHandler
from telegram.constants import ParseMode
import qrcode
from io import BytesIO
import base64
from datetime import datetime

from config import Config
from database import get_db, get_user_by_telegram_id, create_user, update_user_subscription, is_user_subscribed, Payment
from payment_handler import PaymentHandler
from content_generator import ContentGenerator
from ai_agents import AIAgentManager

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TelegramBot:
    def __init__(self):
        self.payment_handler = PaymentHandler()
        self.content_generator = ContentGenerator()
        self.ai_manager = AIAgentManager()
        
    async def start_command(self, update: Update, context):
        """Handle /start command"""
        user = update.effective_user
        chat_id = update.effective_chat.id
        
        # Get or create user in database
        db = next(get_db())
        db_user = get_user_by_telegram_id(db, user.id)
        
        if not db_user:
            db_user = create_user(db, user.id, user.username, user.first_name, user.last_name)
            logger.info(f"New user created: {user.id}")
        
        # Check if user is already subscribed
        if is_user_subscribed(db, user.id):
            await self.send_welcome_back_message(update, context)
            return
        
        # Send welcome message with preview
        welcome_text = f"""
🔥 **Welcome to Paradise Club** 🔥

Hey {user.first_name}! Ready for an unforgettable experience?

✨ **What you get:**
• 🎨 Daily AI-generated exclusive content
• 💬 Chat with 4 gorgeous AI companions
• 🎥 Custom content on request
• 🔒 Private VIP group access
• 💝 Personalized interactions

**Meet your companions:**
• 😘 **Sophia** - Playful & Flirty
• 🌙 **Luna** - Mysterious & Seductive  
• 💕 **Aria** - Sweet & Caring
• 🔥 **Raven** - Bold & Passionate

💰 **Pricing:**
• Basic Access: ${Config.SUBSCRIPTION_PRICE}/month
• Premium VIP: ${Config.PREMIUM_PRICE}/month
• Custom Requests: ${Config.CUSTOM_REQUEST_PRICE} each

Ready to join the fun? 😉
        """
        
        keyboard = [
            [InlineKeyboardButton("🚀 Join Basic ($9.99)", callback_data="subscribe_basic")],
            [InlineKeyboardButton("👑 Join Premium ($29.99)", callback_data="subscribe_premium")],
            [InlineKeyboardButton("👀 Preview Content", callback_data="preview")],
            [InlineKeyboardButton("📱 Share with Friends", callback_data="share")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            welcome_text,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
        
        # Send a preview image
        await self.send_preview_content(chat_id, context)
    
    async def send_preview_content(self, chat_id, context):
        """Send preview content to potential subscribers"""
        try:
            # Generate a preview image
            preview_image = await self.content_generator.generate_preview_image()
            
            if preview_image:
                with open(preview_image, 'rb') as photo:
                    await context.bot.send_photo(
                        chat_id=chat_id,
                        photo=photo,
                        caption="🔥 Just a taste of what's waiting for you inside... 😉"
                    )
        except Exception as e:
            logger.error(f"Error sending preview content: {e}")
    
    async def button_callback(self, update: Update, context):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()
        
        user_id = query.from_user.id
        data = query.data
        
        if data == "subscribe_basic":
            await self.initiate_payment(query, "basic", Config.SUBSCRIPTION_PRICE)
        elif data == "subscribe_premium":
            await self.initiate_payment(query, "premium", Config.PREMIUM_PRICE)
        elif data == "preview":
            await self.send_more_previews(query)
        elif data == "share":
            await self.send_referral_link(query)
        elif data.startswith("agent_"):
            agent_name = data.split("_")[1]
            await self.start_chat_with_agent(query, agent_name)
    
    async def initiate_payment(self, query, subscription_type, amount):
        """Initiate payment process"""
        try:
            # Create invoice
            title = f"Paradise Club - {subscription_type.title()} Subscription"
            description = f"Monthly access to exclusive content and AI companions"
            payload = f"{subscription_type}_{query.from_user.id}_{int(datetime.now().timestamp())}"
            currency = "USD"
            
            prices = [LabeledPrice(label=title, amount=int(amount * 100))]  # Amount in cents
            
            await query.message.reply_invoice(
                title=title,
                description=description,
                payload=payload,
                provider_token=Config.PAYMENT_PROVIDER_TOKEN,
                currency=currency,
                prices=prices,
                start_parameter="payment",
                photo_url="https://example.com/payment-image.jpg"  # Optional
            )
            
        except Exception as e:
            logger.error(f"Payment initiation error: {e}")
            await query.message.reply_text("❌ Payment system temporarily unavailable. Please try again later.")
    
    async def pre_checkout_callback(self, update: Update, context):
        """Handle pre-checkout queries"""
        query = update.pre_checkout_query
        
        # Validate the payment
        if query.invoice_payload.startswith(("basic_", "premium_")):
            await query.answer(ok=True)
        else:
            await query.answer(ok=False, error_message="Invalid payment payload")
    
    async def successful_payment_callback(self, update: Update, context):
        """Handle successful payments"""
        payment = update.message.successful_payment
        user_id = update.effective_user.id
        
        # Parse payload to get subscription type
        payload_parts = payment.invoice_payload.split("_")
        subscription_type = payload_parts[0]
        
        # Update user subscription in database
        db = next(get_db())
        user = get_user_by_telegram_id(db, user_id)
        
        if user:
            # Update subscription
            duration_days = 30 if subscription_type == "basic" else 30  # Both are monthly
            update_user_subscription(db, user.id, subscription_type, duration_days)
            
            # Record payment
            payment_record = Payment(
                user_id=user.id,
                amount=payment.total_amount / 100,  # Convert from cents
                currency=payment.currency,
                transaction_id=payment.telegram_payment_charge_id,
                status="completed",
                payment_type="subscription",
                completed_at=datetime.utcnow()
            )
            db.add(payment_record)
            db.commit()
            
            # Add user to private group
            try:
                await context.bot.unban_chat_member(
                    chat_id=Config.TELEGRAM_GROUP_ID,
                    user_id=user_id
                )
                
                # Create invite link
                invite_link = await context.bot.create_chat_invite_link(
                    chat_id=Config.TELEGRAM_GROUP_ID,
                    member_limit=1,
                    name=f"Invite for {update.effective_user.first_name}"
                )
                
                success_message = f"""
🎉 **Payment Successful!** 🎉

Welcome to Paradise Club, {update.effective_user.first_name}! 

✅ **Your {subscription_type.title()} subscription is now active**
💳 **Transaction ID:** {payment.telegram_payment_charge_id}
📅 **Valid until:** {(datetime.utcnow().replace(day=28) if datetime.utcnow().day > 28 else datetime.utcnow().replace(month=datetime.utcnow().month+1)).strftime('%B %d, %Y')}

🔗 **Join your private group:** {invite_link.invite_link}

**What's next?**
• Join the group to see exclusive content
• Chat with our AI companions
• Request custom content anytime

Ready to have some fun? 😉
                """
                
                # Send success message with agent selection
                keyboard = [
                    [InlineKeyboardButton("💬 Chat with Sophia", callback_data="agent_sophia")],
                    [InlineKeyboardButton("💬 Chat with Luna", callback_data="agent_luna")],
                    [InlineKeyboardButton("💬 Chat with Aria", callback_data="agent_aria")],
                    [InlineKeyboardButton("💬 Chat with Raven", callback_data="agent_raven")],
                    [InlineKeyboardButton("🎨 Request Custom Content", callback_data="custom_request")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    success_message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                
            except Exception as e:
                logger.error(f"Error adding user to group: {e}")
                await update.message.reply_text(
                    "✅ Payment successful! Please contact support to get your group access."
                )
        
        db.close()
    
    async def send_welcome_back_message(self, update: Update, context):
        """Send welcome back message to existing subscribers"""
        user = update.effective_user
        
        message = f"""
👋 **Welcome back, {user.first_name}!** 

Your subscription is still active! 🎉

**Choose what you'd like to do:**
        """
        
        keyboard = [
            [InlineKeyboardButton("💬 Chat with Sophia", callback_data="agent_sophia")],
            [InlineKeyboardButton("💬 Chat with Luna", callback_data="agent_luna")],
            [InlineKeyboardButton("💬 Chat with Aria", callback_data="agent_aria")],
            [InlineKeyboardButton("💬 Chat with Raven", callback_data="agent_raven")],
            [InlineKeyboardButton("🎨 Request Custom Content", callback_data="custom_request")],
            [InlineKeyboardButton("📊 My Account", callback_data="account")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def send_more_previews(self, query):
        """Send additional preview content"""
        preview_text = """
🔥 **More Previews** 🔥

Here's what our members are enjoying:

• 🎨 **Daily fresh content** - New images and videos every day
• 💬 **Interactive chats** - Our AI companions remember your preferences
• 🎯 **Custom requests** - Tell us exactly what you want to see
• 🔒 **100% private** - Secure and discreet platform

Ready to unlock the full experience? 😘
        """
        
        keyboard = [
            [InlineKeyboardButton("🚀 Join Now - Basic ($9.99)", callback_data="subscribe_basic")],
            [InlineKeyboardButton("👑 Join Now - Premium ($29.99)", callback_data="subscribe_premium")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            preview_text,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def send_referral_link(self, query):
        """Send referral link and QR code"""
        user_id = query.from_user.id
        bot_username = (await query.bot.get_me()).username
        referral_link = f"https://t.me/{bot_username}?start=ref_{user_id}"
        
        # Generate QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(referral_link)
        qr.make(fit=True)
        
        qr_image = qr.make_image(fill_color="black", back_color="white")
        qr_buffer = BytesIO()
        qr_image.save(qr_buffer, format='PNG')
        qr_buffer.seek(0)
        
        share_text = f"""
📱 **Share Paradise Club** 📱

Invite your friends and earn rewards! 

🔗 **Your referral link:**
{referral_link}

📱 **QR Code:** (attached below)

💰 **Earn $5 for each friend who subscribes!**
        """
        
        await query.message.reply_text(share_text, parse_mode=ParseMode.MARKDOWN)
        await query.message.reply_photo(
            photo=qr_buffer,
            caption="📱 Share this QR code with friends!"
        )
    
    async def start_chat_with_agent(self, query, agent_name):
        """Start chat session with AI agent"""
        user_id = query.from_user.id
        
        # Check if user is subscribed
        db = next(get_db())
        if not is_user_subscribed(db, user_id):
            await query.message.reply_text("❌ Please subscribe first to chat with our companions!")
            return
        
        # Start chat session
        agent_info = Config.AI_AGENTS.get(agent_name, {})
        agent_display_name = agent_info.get('name', agent_name.title())
        
        chat_text = f"""
💕 **Starting chat with {agent_display_name}** 💕

{agent_display_name} is excited to meet you! Just send any message to start chatting.

Type /end_chat when you want to finish this conversation.
        """
        
        await query.edit_message_text(chat_text, parse_mode=ParseMode.MARKDOWN)
        
        # Set user's current agent in context
        context = query._bot._application.user_data.get(user_id, {})
        context['current_agent'] = agent_name
        query._bot._application.user_data[user_id] = context
    
    async def handle_message(self, update: Update, context):
        """Handle regular messages (for AI chat)"""
        user_id = update.effective_user.id
        message_text = update.message.text
        
        # Check if user is in a chat session
        user_context = context.user_data.get('current_agent')
        
        if user_context:
            # Generate AI response
            response = await self.ai_manager.generate_response(
                user_context, message_text, user_id
            )
            
            await update.message.reply_text(response, parse_mode=ParseMode.MARKDOWN)
        else:
            # Default response for non-chat messages
            await update.message.reply_text(
                "💬 Use /start to begin or select an AI companion to chat with!"
            )
    
    async def end_chat_command(self, update: Update, context):
        """End current chat session"""
        user_id = update.effective_user.id
        
        if 'current_agent' in context.user_data:
            agent_name = context.user_data['current_agent']
            del context.user_data['current_agent']
            
            await update.message.reply_text(
                f"💕 Chat with {agent_name.title()} ended. Thanks for the fun time! 😘\n\n"
                "Use /start to chat with another companion or access other features."
            )
        else:
            await update.message.reply_text("❌ You're not currently in a chat session.")

def main():
    """Start the bot"""
    # Create application
    application = Application.builder().token(Config.TELEGRAM_BOT_TOKEN).build()
    
    # Create bot instance
    bot = TelegramBot()
    
    # Add handlers
    application.add_handler(CommandHandler("start", bot.start_command))
    application.add_handler(CommandHandler("end_chat", bot.end_chat_command))
    application.add_handler(CallbackQueryHandler(bot.button_callback))
    application.add_handler(PreCheckoutQueryHandler(bot.pre_checkout_callback))
    application.add_handler(MessageHandler(filters.SUCCESSFUL_PAYMENT, bot.successful_payment_callback))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, bot.handle_message))
    
    # Start the bot
    logger.info("Starting Telegram bot...")
    application.run_polling()

if __name__ == '__main__':
    main()
