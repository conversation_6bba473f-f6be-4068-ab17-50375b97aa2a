import logging
import re
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import hashlib
import base64
from PIL import Image
import requests

from config import Config
from database import get_db, User, get_user_by_telegram_id

logger = logging.getLogger(__name__)

class ComplianceManager:
    def __init__(self):
        self.blocked_words = self._load_blocked_words()
        self.age_verification_required = True
        self.content_moderation_enabled = True
        
        # Age verification settings
        self.min_age = 18
        self.verification_methods = ['id_document', 'credit_card', 'phone_verification']
        
        # Content filtering settings
        self.image_moderation_api = None  # Can integrate with AWS Rekognition, Google Vision, etc.
        
    def _load_blocked_words(self) -> List[str]:
        """Load list of blocked words and phrases"""
        blocked_words = [
            # Age-related terms
            'minor', 'child', 'kid', 'young', 'teen', 'underage', 'school',
            'student', 'high school', 'middle school', 'elementary',
            
            # Illegal content
            'illegal', 'drugs', 'violence', 'harm', 'abuse', 'exploitation',
            'trafficking', 'non-consensual', 'revenge', 'blackmail',
            
            # Platform violations
            'spam', 'scam', 'fraud', 'fake', 'impersonation', 'harassment',
            'hate speech', 'discrimination', 'doxxing', 'stalking'
        ]
        
        return blocked_words
    
    async def verify_user_age(self, user_id: int, verification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Verify user age using provided verification method"""
        try:
            verification_method = verification_data.get('method')
            
            if verification_method not in self.verification_methods:
                return {
                    'verified': False,
                    'error': 'Invalid verification method'
                }
            
            if verification_method == 'id_document':
                return await self._verify_id_document(user_id, verification_data)
            elif verification_method == 'credit_card':
                return await self._verify_credit_card(user_id, verification_data)
            elif verification_method == 'phone_verification':
                return await self._verify_phone_number(user_id, verification_data)
            
            return {
                'verified': False,
                'error': 'Verification method not implemented'
            }
            
        except Exception as e:
            logger.error(f"Age verification error: {e}")
            return {
                'verified': False,
                'error': 'Verification system error'
            }
    
    async def _verify_id_document(self, user_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """Verify age using ID document (requires manual review)"""
        try:
            document_image = data.get('document_image')
            document_type = data.get('document_type', 'id_card')
            
            if not document_image:
                return {
                    'verified': False,
                    'error': 'Document image required'
                }
            
            # Save document for manual review
            verification_id = await self._save_verification_request(
                user_id, 'id_document', data
            )
            
            # In a real implementation, you would:
            # 1. Use OCR to extract text from document
            # 2. Verify document authenticity
            # 3. Extract birth date and calculate age
            # 4. Store verification result
            
            return {
                'verified': False,  # Pending manual review
                'pending': True,
                'verification_id': verification_id,
                'message': 'Document submitted for review. You will be notified within 24 hours.'
            }
            
        except Exception as e:
            logger.error(f"ID document verification error: {e}")
            return {
                'verified': False,
                'error': 'Document verification failed'
            }
    
    async def _verify_credit_card(self, user_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """Verify age using credit card (18+ requirement)"""
        try:
            # Credit card verification implies 18+ age
            # This would integrate with payment processor for verification
            
            card_token = data.get('card_token')
            if not card_token:
                return {
                    'verified': False,
                    'error': 'Credit card token required'
                }
            
            # Simulate credit card verification
            # In real implementation, verify with Stripe/payment processor
            verification_result = await self._verify_with_payment_processor(card_token)
            
            if verification_result.get('valid'):
                await self._mark_user_verified(user_id, 'credit_card')
                return {
                    'verified': True,
                    'method': 'credit_card',
                    'message': 'Age verified successfully'
                }
            
            return {
                'verified': False,
                'error': 'Credit card verification failed'
            }
            
        except Exception as e:
            logger.error(f"Credit card verification error: {e}")
            return {
                'verified': False,
                'error': 'Credit card verification failed'
            }
    
    async def _verify_phone_number(self, user_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """Verify age using phone number (basic verification)"""
        try:
            phone_number = data.get('phone_number')
            verification_code = data.get('verification_code')
            
            if not phone_number:
                return {
                    'verified': False,
                    'error': 'Phone number required'
                }
            
            # Basic phone verification (not age-specific)
            # This is the weakest form of age verification
            if verification_code:
                # Verify the code (implement SMS verification)
                if await self._verify_sms_code(phone_number, verification_code):
                    await self._mark_user_verified(user_id, 'phone_verification')
                    return {
                        'verified': True,
                        'method': 'phone_verification',
                        'message': 'Phone verified successfully'
                    }
                else:
                    return {
                        'verified': False,
                        'error': 'Invalid verification code'
                    }
            else:
                # Send verification code
                await self._send_sms_verification(phone_number)
                return {
                    'verified': False,
                    'code_sent': True,
                    'message': 'Verification code sent to your phone'
                }
                
        except Exception as e:
            logger.error(f"Phone verification error: {e}")
            return {
                'verified': False,
                'error': 'Phone verification failed'
            }
    
    async def _save_verification_request(self, user_id: int, method: str, data: Dict[str, Any]) -> str:
        """Save verification request for manual review"""
        try:
            # Generate verification ID
            verification_id = hashlib.md5(f"{user_id}_{method}_{datetime.utcnow()}".encode()).hexdigest()
            
            # In a real implementation, save to database
            # verification_requests table with status tracking
            
            logger.info(f"Verification request saved: {verification_id}")
            return verification_id
            
        except Exception as e:
            logger.error(f"Error saving verification request: {e}")
            return ""
    
    async def _verify_with_payment_processor(self, card_token: str) -> Dict[str, Any]:
        """Verify credit card with payment processor"""
        try:
            # Simulate payment processor verification
            # In real implementation, use Stripe, PayPal, etc.
            
            # Basic validation
            if len(card_token) < 10:
                return {'valid': False, 'error': 'Invalid card token'}
            
            return {'valid': True}
            
        except Exception as e:
            logger.error(f"Payment processor verification error: {e}")
            return {'valid': False, 'error': 'Verification failed'}
    
    async def _verify_sms_code(self, phone_number: str, code: str) -> bool:
        """Verify SMS verification code"""
        try:
            # In real implementation, verify with SMS service (Twilio, etc.)
            # For demo purposes, accept any 6-digit code
            return len(code) == 6 and code.isdigit()
            
        except Exception as e:
            logger.error(f"SMS verification error: {e}")
            return False
    
    async def _send_sms_verification(self, phone_number: str) -> bool:
        """Send SMS verification code"""
        try:
            # In real implementation, send SMS using Twilio, AWS SNS, etc.
            verification_code = f"{random.randint(100000, 999999)}"
            
            # Store code temporarily (Redis, database, etc.)
            # For demo purposes, just log it
            logger.info(f"SMS verification code for {phone_number}: {verification_code}")
            
            return True
            
        except Exception as e:
            logger.error(f"SMS sending error: {e}")
            return False
    
    async def _mark_user_verified(self, user_id: int, method: str):
        """Mark user as age verified"""
        try:
            db = next(get_db())
            user = get_user_by_telegram_id(db, user_id)
            
            if user:
                # Add verification info to user record
                # In real implementation, add verification fields to User model
                logger.info(f"User {user_id} verified using {method}")
            
            db.close()
            
        except Exception as e:
            logger.error(f"Error marking user verified: {e}")
    
    async def moderate_text_content(self, text: str) -> Dict[str, Any]:
        """Moderate text content for inappropriate material"""
        try:
            # Check for blocked words
            text_lower = text.lower()
            found_violations = []
            
            for word in self.blocked_words:
                if word in text_lower:
                    found_violations.append(word)
            
            # Check for patterns
            patterns = [
                r'\b\d{1,2}[\s\-]?years?\s+old\b',  # Age mentions
                r'\bunder\s+\d{1,2}\b',  # Under age mentions
                r'\b(teen|young|minor)\b',  # Age-related terms
            ]
            
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    found_violations.append(f"Pattern: {pattern}")
            
            is_safe = len(found_violations) == 0
            
            return {
                'safe': is_safe,
                'violations': found_violations,
                'confidence': 0.9 if found_violations else 0.95,
                'action': 'block' if not is_safe else 'allow'
            }
            
        except Exception as e:
            logger.error(f"Text moderation error: {e}")
            return {
                'safe': False,
                'violations': ['moderation_error'],
                'confidence': 0.0,
                'action': 'block'
            }
    
    async def moderate_image_content(self, image_path: str) -> Dict[str, Any]:
        """Moderate image content"""
        try:
            # Basic image checks
            if not os.path.exists(image_path):
                return {
                    'safe': False,
                    'violations': ['file_not_found'],
                    'confidence': 1.0,
                    'action': 'block'
                }
            
            # Check file size and format
            try:
                with Image.open(image_path) as img:
                    width, height = img.size
                    
                    # Basic safety checks
                    if width < 100 or height < 100:
                        return {
                            'safe': False,
                            'violations': ['image_too_small'],
                            'confidence': 0.8,
                            'action': 'block'
                        }
                    
                    # In real implementation, use AI moderation services:
                    # - AWS Rekognition
                    # - Google Cloud Vision API
                    # - Microsoft Azure Content Moderator
                    # - Custom ML models
                    
                    return {
                        'safe': True,
                        'violations': [],
                        'confidence': 0.85,
                        'action': 'allow'
                    }
                    
            except Exception as e:
                logger.error(f"Image processing error: {e}")
                return {
                    'safe': False,
                    'violations': ['invalid_image'],
                    'confidence': 1.0,
                    'action': 'block'
                }
                
        except Exception as e:
            logger.error(f"Image moderation error: {e}")
            return {
                'safe': False,
                'violations': ['moderation_error'],
                'confidence': 0.0,
                'action': 'block'
            }
    
    async def check_user_compliance(self, user_id: int) -> Dict[str, Any]:
        """Check if user meets all compliance requirements"""
        try:
            db = next(get_db())
            user = get_user_by_telegram_id(db, user_id)
            
            if not user:
                return {
                    'compliant': False,
                    'issues': ['user_not_found']
                }
            
            issues = []
            
            # Check age verification
            if self.age_verification_required:
                # In real implementation, check user verification status
                # For demo, assume verification is required
                issues.append('age_verification_required')
            
            # Check account status
            if not user.is_active:
                issues.append('account_inactive')
            
            # Check subscription status
            if not user.subscription_end or user.subscription_end < datetime.utcnow():
                issues.append('subscription_expired')
            
            db.close()
            
            return {
                'compliant': len(issues) == 0,
                'issues': issues,
                'user_id': user_id
            }
            
        except Exception as e:
            logger.error(f"Compliance check error: {e}")
            return {
                'compliant': False,
                'issues': ['compliance_check_error']
            }
    
    async def generate_compliance_report(self) -> Dict[str, Any]:
        """Generate compliance report"""
        try:
            db = next(get_db())
            
            # Get user statistics
            total_users = db.query(User).count()
            active_users = db.query(User).filter(User.is_active == True).count()
            verified_users = 0  # In real implementation, count verified users
            
            # Get recent activity
            recent_signups = db.query(User).filter(
                User.created_at >= datetime.utcnow() - timedelta(days=7)
            ).count()
            
            db.close()
            
            return {
                'report_date': datetime.utcnow().isoformat(),
                'total_users': total_users,
                'active_users': active_users,
                'verified_users': verified_users,
                'verification_rate': round((verified_users / total_users * 100) if total_users > 0 else 0, 2),
                'recent_signups': recent_signups,
                'compliance_status': 'operational'
            }
            
        except Exception as e:
            logger.error(f"Compliance report error: {e}")
            return {
                'report_date': datetime.utcnow().isoformat(),
                'error': str(e),
                'compliance_status': 'error'
            }

# Utility functions
async def verify_user_age_simple(user_id: int, birth_date: str) -> bool:
    """Simple age verification using birth date"""
    try:
        birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d')
        age = (datetime.now() - birth_date_obj).days // 365
        return age >= 18
    except Exception:
        return False

def sanitize_user_input(text: str) -> str:
    """Sanitize user input"""
    # Remove potentially harmful characters
    sanitized = re.sub(r'[<>"\']', '', text)
    # Limit length
    sanitized = sanitized[:500]
    return sanitized.strip()
