import asyncio
import logging
import schedule
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
import random
import os

from telegram import <PERSON><PERSON>
from telegram.error import TelegramError

from config import Config
from database import get_db, GeneratedContent, User
from content_generator import ContentGenerator

logger = logging.getLogger(__name__)

class ContentScheduler:
    def __init__(self):
        self.bot = Bot(token=Config.TELEGRAM_BOT_TOKEN)
        self.content_generator = ContentGenerator()
        self.group_id = Config.TELEGRAM_GROUP_ID
        self.is_running = False
        
        # Content posting schedule
        self.posting_times = [
            "09:00",  # Morning
            "13:00",  # Afternoon  
            "18:00",  # Evening
            "22:00"   # Night
        ]
        
        # Setup schedule
        self._setup_schedule()
    
    def _setup_schedule(self):
        """Setup content posting schedule"""
        try:
            # Schedule content generation (daily at 6 AM)
            schedule.every().day.at("06:00").do(self._generate_daily_content)
            
            # Schedule content posting at different times
            for posting_time in self.posting_times:
                schedule.every().day.at(posting_time).do(self._post_scheduled_content)
            
            # Schedule cleanup (weekly)
            schedule.every().sunday.at("02:00").do(self._cleanup_old_content)
            
            logger.info("Content scheduler setup complete")
            
        except Exception as e:
            logger.error(f"Error setting up schedule: {e}")
    
    async def _generate_daily_content(self):
        """Generate daily content batch"""
        try:
            logger.info("Starting daily content generation...")
            
            # Generate content batch
            generated_files = await self.content_generator.generate_daily_content()
            
            logger.info(f"Generated {len(generated_files)} content items for today")
            
            # Send notification to admins
            await self._notify_admins(f"✅ Daily content generated: {len(generated_files)} items")
            
        except Exception as e:
            logger.error(f"Error in daily content generation: {e}")
            await self._notify_admins(f"❌ Daily content generation failed: {str(e)}")
    
    async def _post_scheduled_content(self):
        """Post scheduled content to group"""
        try:
            db = next(get_db())
            
            # Get unposted content
            unposted_content = db.query(GeneratedContent).filter(
                GeneratedContent.is_posted == False,
                GeneratedContent.created_at >= datetime.utcnow() - timedelta(days=1)
            ).limit(1).all()
            
            if not unposted_content:
                logger.info("No unposted content available")
                db.close()
                return
            
            content = unposted_content[0]
            
            # Post content to group
            success = await self._post_content_to_group(content)
            
            if success:
                # Mark as posted
                content.is_posted = True
                content.posted_at = datetime.utcnow()
                db.commit()
                
                logger.info(f"Posted content: {content.file_path}")
            else:
                logger.error(f"Failed to post content: {content.file_path}")
            
            db.close()
            
        except Exception as e:
            logger.error(f"Error posting scheduled content: {e}")
    
    async def _post_content_to_group(self, content: GeneratedContent) -> bool:
        """Post content to Telegram group"""
        try:
            if not os.path.exists(content.file_path):
                logger.error(f"Content file not found: {content.file_path}")
                return False
            
            # Prepare caption
            captions = [
                "🔥 Fresh content just for you! 😘",
                "✨ Something special to brighten your day 💕",
                "🎨 New exclusive content! Hope you love it 😉",
                "💖 Made with love for our VIP members 🥰",
                "🌟 Another masterpiece for your collection! 🔥"
            ]
            
            caption = random.choice(captions)
            
            # Post based on content type
            if content.content_type == "image":
                with open(content.file_path, 'rb') as photo:
                    await self.bot.send_photo(
                        chat_id=self.group_id,
                        photo=photo,
                        caption=caption
                    )
            
            elif content.content_type == "video":
                with open(content.file_path, 'rb') as video:
                    await self.bot.send_video(
                        chat_id=self.group_id,
                        video=video,
                        caption=caption
                    )
            
            return True
            
        except TelegramError as e:
            logger.error(f"Telegram error posting content: {e}")
            return False
        except Exception as e:
            logger.error(f"Error posting content to group: {e}")
            return False
    
    async def _cleanup_old_content(self):
        """Cleanup old content files and database records"""
        try:
            logger.info("Starting content cleanup...")
            
            db = next(get_db())
            
            # Get content older than 30 days
            old_content = db.query(GeneratedContent).filter(
                GeneratedContent.created_at < datetime.utcnow() - timedelta(days=30)
            ).all()
            
            deleted_files = 0
            deleted_records = 0
            
            for content in old_content:
                try:
                    # Delete file if exists
                    if os.path.exists(content.file_path):
                        os.remove(content.file_path)
                        deleted_files += 1
                    
                    # Delete database record
                    db.delete(content)
                    deleted_records += 1
                    
                except Exception as e:
                    logger.error(f"Error deleting content {content.id}: {e}")
            
            db.commit()
            db.close()
            
            logger.info(f"Cleanup complete: {deleted_files} files, {deleted_records} records deleted")
            await self._notify_admins(f"🧹 Cleanup complete: {deleted_files} files, {deleted_records} records deleted")
            
        except Exception as e:
            logger.error(f"Error in content cleanup: {e}")
            await self._notify_admins(f"❌ Cleanup failed: {str(e)}")
    
    async def _notify_admins(self, message: str):
        """Send notification to admin users"""
        try:
            for admin_id in Config.ADMIN_USER_IDS:
                try:
                    await self.bot.send_message(
                        chat_id=admin_id,
                        text=f"🤖 **Paradise Club Bot**\n\n{message}",
                        parse_mode='Markdown'
                    )
                except Exception as e:
                    logger.error(f"Error notifying admin {admin_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Error in admin notification: {e}")
    
    async def post_welcome_message(self):
        """Post welcome message to group"""
        try:
            welcome_messages = [
                "🎉 **Welcome to Paradise Club!** 🎉\n\nGet ready for exclusive content and amazing conversations with our AI companions! 💕",
                "✨ **Paradise Club is now active!** ✨\n\nYour premium entertainment experience starts now! 🔥",
                "🌟 **Welcome to your VIP experience!** 🌟\n\nEnjoy exclusive content and chat with Sophia, Luna, Aria, and Raven! 😘"
            ]
            
            message = random.choice(welcome_messages)
            
            await self.bot.send_message(
                chat_id=self.group_id,
                text=message,
                parse_mode='Markdown'
            )
            
            logger.info("Welcome message posted to group")
            
        except Exception as e:
            logger.error(f"Error posting welcome message: {e}")
    
    async def post_custom_content(self, file_path: str, caption: str = None):
        """Post custom content immediately"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"Custom content file not found: {file_path}")
                return False
            
            if not caption:
                caption = "🎨 Custom content just for you! 💕"
            
            # Determine content type by file extension
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext in ['.jpg', '.jpeg', '.png', '.gif']:
                with open(file_path, 'rb') as photo:
                    await self.bot.send_photo(
                        chat_id=self.group_id,
                        photo=photo,
                        caption=caption
                    )
            
            elif file_ext in ['.mp4', '.avi', '.mov', '.mkv']:
                with open(file_path, 'rb') as video:
                    await self.bot.send_video(
                        chat_id=self.group_id,
                        video=video,
                        caption=caption
                    )
            
            else:
                logger.error(f"Unsupported file type: {file_ext}")
                return False
            
            logger.info(f"Custom content posted: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error posting custom content: {e}")
            return False
    
    async def get_posting_stats(self) -> dict:
        """Get content posting statistics"""
        try:
            db = next(get_db())
            
            # Today's stats
            today = datetime.utcnow().date()
            today_posted = db.query(GeneratedContent).filter(
                GeneratedContent.is_posted == True,
                GeneratedContent.posted_at >= today
            ).count()
            
            # This week's stats
            week_start = datetime.utcnow() - timedelta(days=7)
            week_posted = db.query(GeneratedContent).filter(
                GeneratedContent.is_posted == True,
                GeneratedContent.posted_at >= week_start
            ).count()
            
            # Total stats
            total_posted = db.query(GeneratedContent).filter(
                GeneratedContent.is_posted == True
            ).count()
            
            total_generated = db.query(GeneratedContent).count()
            
            # Pending content
            pending_content = db.query(GeneratedContent).filter(
                GeneratedContent.is_posted == False
            ).count()
            
            db.close()
            
            return {
                'today_posted': today_posted,
                'week_posted': week_posted,
                'total_posted': total_posted,
                'total_generated': total_generated,
                'pending_content': pending_content,
                'posting_rate': round((total_posted / total_generated * 100) if total_generated > 0 else 0, 1)
            }
            
        except Exception as e:
            logger.error(f"Error getting posting stats: {e}")
            return {}
    
    def start_scheduler(self):
        """Start the content scheduler"""
        self.is_running = True
        logger.info("Content scheduler started")
        
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except KeyboardInterrupt:
                logger.info("Scheduler stopped by user")
                break
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                time.sleep(60)
    
    def stop_scheduler(self):
        """Stop the content scheduler"""
        self.is_running = False
        logger.info("Content scheduler stopped")
    
    async def run_async_scheduler(self):
        """Run scheduler with async support"""
        self.is_running = True
        logger.info("Async content scheduler started")
        
        while self.is_running:
            try:
                schedule.run_pending()
                await asyncio.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Async scheduler error: {e}")
                await asyncio.sleep(60)

# Standalone scheduler runner
async def main():
    """Main function to run the scheduler"""
    scheduler = ContentScheduler()
    
    # Post welcome message on startup
    await scheduler.post_welcome_message()
    
    # Start scheduler
    await scheduler.run_async_scheduler()

if __name__ == "__main__":
    asyncio.run(main())
