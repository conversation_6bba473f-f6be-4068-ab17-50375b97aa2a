import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Telegram Bot Configuration
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
    TELEGRAM_GROUP_ID = os.getenv('TELEGRAM_GROUP_ID')  # Your private group ID
    WEBHOOK_URL = os.getenv('WEBHOOK_URL', 'https://yourdomain.com')
    
    # Payment Configuration
    STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY')
    STRIPE_PUBLISHABLE_KEY = os.getenv('STRIPE_PUBLISHABLE_KEY')
    PAYMENT_PROVIDER_TOKEN = os.getenv('PAYMENT_PROVIDER_TOKEN')  # Telegram payment provider
    
    # AI Configuration
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    HUGGINGFACE_API_TOKEN = os.getenv('HUGGINGFACE_API_TOKEN')
    REPLICATE_API_TOKEN = os.getenv('REPLICATE_API_TOKEN', '')
    
    # Database Configuration
    DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql://user:password@localhost/telegram_platform')
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379')
    
    # Platform Configuration
    SUBSCRIPTION_PRICE = float(os.getenv('SUBSCRIPTION_PRICE', '9.99'))  # USD
    PREMIUM_PRICE = float(os.getenv('PREMIUM_PRICE', '29.99'))  # USD
    CUSTOM_REQUEST_PRICE = float(os.getenv('CUSTOM_REQUEST_PRICE', '15.99'))  # USD
    
    # Content Generation
    CONTENT_POSTING_INTERVAL = int(os.getenv('CONTENT_POSTING_INTERVAL', '3600'))  # seconds
    MAX_DAILY_CONTENT = int(os.getenv('MAX_DAILY_CONTENT', '10'))
    
    # Security
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-change-this')
    ADMIN_USER_IDS = [int(x) for x in os.getenv('ADMIN_USER_IDS', '').split(',') if x]
    
    # File Storage
    MEDIA_STORAGE_PATH = os.getenv('MEDIA_STORAGE_PATH', './media')
    MAX_FILE_SIZE = int(os.getenv('MAX_FILE_SIZE', '50')) * 1024 * 1024  # 50MB
    
    # AI Agent Personalities
    AI_AGENTS = {
        'sophia': {
            'name': 'Sophia',
            'personality': 'Flirty and playful, loves to tease and make jokes',
            'avatar': 'sophia_avatar.jpg'
        },
        'luna': {
            'name': 'Luna',
            'personality': 'Mysterious and seductive, speaks in riddles',
            'avatar': 'luna_avatar.jpg'
        },
        'aria': {
            'name': 'Aria',
            'personality': 'Sweet and caring, loves deep conversations',
            'avatar': 'aria_avatar.jpg'
        },
        'raven': {
            'name': 'Raven',
            'personality': 'Bold and confident, direct and passionate',
            'avatar': 'raven_avatar.jpg'
        }
    }
