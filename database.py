from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Float, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime, timedelta
from config import Config

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    telegram_id = Column(Integer, unique=True, nullable=False)
    username = Column(String(100))
    first_name = Column(String(100))
    last_name = Column(String(100))
    phone_number = Column(String(20))
    email = Column(String(100))
    
    # Subscription info
    subscription_type = Column(String(20), default='none')  # none, basic, premium
    subscription_start = Column(DateTime)
    subscription_end = Column(DateTime)
    is_active = Column(Boolean, default=False)
    
    # Payment info
    total_paid = Column(Float, default=0.0)
    last_payment = Column(DateTime)
    
    # User preferences
    preferred_agent = Column(String(50))
    content_preferences = Column(Text)  # JSON string
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    last_active = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    payments = relationship("Payment", back_populates="user")
    content_requests = relationship("ContentRequest", back_populates="user")
    chat_sessions = relationship("ChatSession", back_populates="user")

class Payment(Base):
    __tablename__ = 'payments'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    
    # Payment details
    amount = Column(Float, nullable=False)
    currency = Column(String(3), default='USD')
    payment_method = Column(String(50))
    transaction_id = Column(String(100), unique=True)
    
    # Payment status
    status = Column(String(20), default='pending')  # pending, completed, failed, refunded
    payment_type = Column(String(20))  # subscription, premium, custom_request
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # Relationships
    user = relationship("User", back_populates="payments")

class ContentRequest(Base):
    __tablename__ = 'content_requests'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    
    # Request details
    request_type = Column(String(20))  # image, video, custom
    description = Column(Text)
    style_preferences = Column(Text)  # JSON string
    
    # Status
    status = Column(String(20), default='pending')  # pending, processing, completed, failed
    generated_content_path = Column(String(500))
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # Relationships
    user = relationship("User", back_populates="content_requests")

class ChatSession(Base):
    __tablename__ = 'chat_sessions'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    
    # Chat details
    agent_name = Column(String(50))
    session_start = Column(DateTime, default=datetime.utcnow)
    session_end = Column(DateTime)
    message_count = Column(Integer, default=0)
    
    # Relationships
    user = relationship("User", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="session")

class ChatMessage(Base):
    __tablename__ = 'chat_messages'
    
    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, ForeignKey('chat_sessions.id'))
    
    # Message details
    sender_type = Column(String(10))  # user, agent
    message_text = Column(Text)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    session = relationship("ChatSession", back_populates="messages")

class GeneratedContent(Base):
    __tablename__ = 'generated_content'
    
    id = Column(Integer, primary_key=True)
    
    # Content details
    content_type = Column(String(20))  # image, video
    file_path = Column(String(500))
    prompt_used = Column(Text)
    generation_model = Column(String(100))
    
    # Metadata
    file_size = Column(Integer)
    duration = Column(Float)  # for videos
    resolution = Column(String(20))
    
    # Status
    is_posted = Column(Boolean, default=False)
    posted_at = Column(DateTime)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)

# Database setup
engine = create_engine(Config.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    Base.metadata.create_all(bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Utility functions
def get_user_by_telegram_id(db, telegram_id: int):
    return db.query(User).filter(User.telegram_id == telegram_id).first()

def create_user(db, telegram_id: int, username: str = None, first_name: str = None, last_name: str = None):
    user = User(
        telegram_id=telegram_id,
        username=username,
        first_name=first_name,
        last_name=last_name
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user

def update_user_subscription(db, user_id: int, subscription_type: str, duration_days: int = 30):
    user = db.query(User).filter(User.id == user_id).first()
    if user:
        user.subscription_type = subscription_type
        user.subscription_start = datetime.utcnow()
        user.subscription_end = datetime.utcnow() + timedelta(days=duration_days)
        user.is_active = True
        db.commit()
    return user

def is_user_subscribed(db, telegram_id: int):
    user = get_user_by_telegram_id(db, telegram_id)
    if not user:
        return False
    
    if user.subscription_end and user.subscription_end > datetime.utcnow():
        return True
    return False
