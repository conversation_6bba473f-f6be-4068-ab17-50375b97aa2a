import os
import asyncio
import logging
import random
import requests
from datetime import datetime
from typing import Optional, List, Dict, Any
from PIL import Image, ImageEnhance, ImageFilter
import torch
from diffusers import StableDiffusionPipeline
import httpx

from config import Config
from database import get_db, GeneratedContent

logger = logging.getLogger(__name__)

class ContentGenerator:
    def __init__(self):
        self.huggingface_token = Config.HUGGINGFACE_API_TOKEN
        self.replicate_token = Config.REPLICATE_API_TOKEN
        self.media_path = Config.MEDIA_STORAGE_PATH
        
        # Ensure media directory exists
        os.makedirs(self.media_path, exist_ok=True)
        os.makedirs(f"{self.media_path}/images", exist_ok=True)
        os.makedirs(f"{self.media_path}/videos", exist_ok=True)
        os.makedirs(f"{self.media_path}/previews", exist_ok=True)
        
        # Initialize local Stable Diffusion if available
        self.local_pipeline = None
        self._init_local_pipeline()
        
        # Content prompts for different styles
        self.image_prompts = {
            'artistic': [
                "beautiful woman, artistic portrait, soft lighting, elegant pose, high quality, detailed",
                "stunning model, professional photography, glamour shot, perfect lighting, 4k resolution",
                "gorgeous lady, fashion photography, studio lighting, elegant dress, sophisticated",
                "beautiful portrait, soft focus, natural beauty, professional model, high fashion"
            ],
            'fantasy': [
                "fantasy goddess, ethereal beauty, magical atmosphere, glowing effects, mystical",
                "enchanted princess, fairy tale setting, magical lighting, dreamy atmosphere",
                "celestial beauty, starlight background, magical aura, fantasy art style",
                "mythical goddess, divine beauty, heavenly lighting, fantasy portrait"
            ],
            'vintage': [
                "vintage pin-up style, retro fashion, classic beauty, 1950s aesthetic",
                "classic Hollywood glamour, vintage photography, timeless elegance",
                "retro model, vintage styling, classic pose, old Hollywood charm",
                "pin-up girl, vintage aesthetic, classic beauty, retro glamour"
            ]
        }
        
        self.video_prompts = [
            "elegant dance movement, graceful motion, soft lighting",
            "fashion model walking, confident stride, professional lighting",
            "artistic pose sequence, flowing movement, cinematic quality",
            "glamour shot transition, smooth camera movement, high quality"
        ]
    
    def _init_local_pipeline(self):
        """Initialize local Stable Diffusion pipeline if GPU is available"""
        try:
            if torch.cuda.is_available():
                self.local_pipeline = StableDiffusionPipeline.from_pretrained(
                    "runwayml/stable-diffusion-v1-5",
                    torch_dtype=torch.float16
                ).to("cuda")
                logger.info("Local Stable Diffusion pipeline initialized")
            else:
                logger.info("CUDA not available, using API-based generation")
        except Exception as e:
            logger.error(f"Failed to initialize local pipeline: {e}")
    
    async def generate_image_huggingface(self, prompt: str, style: str = "artistic") -> Optional[str]:
        """Generate image using Hugging Face API"""
        try:
            api_url = "https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5"
            headers = {"Authorization": f"Bearer {self.huggingface_token}"}
            
            # Enhance prompt based on style
            enhanced_prompt = self._enhance_prompt(prompt, style)
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    api_url,
                    headers=headers,
                    json={"inputs": enhanced_prompt}
                )
                
                if response.status_code == 200:
                    # Save image
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"hf_image_{timestamp}_{random.randint(1000, 9999)}.jpg"
                    filepath = os.path.join(self.media_path, "images", filename)
                    
                    with open(filepath, "wb") as f:
                        f.write(response.content)
                    
                    # Post-process image
                    processed_path = await self._post_process_image(filepath)
                    
                    # Save to database
                    await self._save_generated_content(
                        content_type="image",
                        file_path=processed_path,
                        prompt_used=enhanced_prompt,
                        generation_model="huggingface-stable-diffusion"
                    )
                    
                    logger.info(f"Image generated successfully: {processed_path}")
                    return processed_path
                else:
                    logger.error(f"Hugging Face API error: {response.status_code}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error generating image with Hugging Face: {e}")
            return None
    
    async def generate_image_local(self, prompt: str, style: str = "artistic") -> Optional[str]:
        """Generate image using local Stable Diffusion"""
        if not self.local_pipeline:
            return None
        
        try:
            enhanced_prompt = self._enhance_prompt(prompt, style)
            
            # Generate image
            with torch.autocast("cuda"):
                image = self.local_pipeline(
                    enhanced_prompt,
                    num_inference_steps=20,
                    guidance_scale=7.5,
                    height=512,
                    width=512
                ).images[0]
            
            # Save image
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"local_image_{timestamp}_{random.randint(1000, 9999)}.jpg"
            filepath = os.path.join(self.media_path, "images", filename)
            
            image.save(filepath, quality=95)
            
            # Post-process image
            processed_path = await self._post_process_image(filepath)
            
            # Save to database
            await self._save_generated_content(
                content_type="image",
                file_path=processed_path,
                prompt_used=enhanced_prompt,
                generation_model="local-stable-diffusion"
            )
            
            logger.info(f"Local image generated successfully: {processed_path}")
            return processed_path
            
        except Exception as e:
            logger.error(f"Error generating local image: {e}")
            return None
    
    async def generate_video_replicate(self, prompt: str) -> Optional[str]:
        """Generate video using Replicate API"""
        try:
            if not self.replicate_token:
                logger.warning("Replicate token not available")
                return None
            
            api_url = "https://api.replicate.com/v1/predictions"
            headers = {
                "Authorization": f"Token {self.replicate_token}",
                "Content-Type": "application/json"
            }
            
            # Use a video generation model (example: RunwayML Gen-2)
            data = {
                "version": "model-version-id",  # Replace with actual model version
                "input": {
                    "prompt": prompt,
                    "duration": 3,  # 3 seconds
                    "fps": 24
                }
            }
            
            async with httpx.AsyncClient(timeout=120.0) as client:
                # Start prediction
                response = await client.post(api_url, headers=headers, json=data)
                
                if response.status_code == 201:
                    prediction = response.json()
                    prediction_id = prediction["id"]
                    
                    # Poll for completion
                    for _ in range(30):  # Wait up to 5 minutes
                        await asyncio.sleep(10)
                        
                        status_response = await client.get(
                            f"{api_url}/{prediction_id}",
                            headers=headers
                        )
                        
                        if status_response.status_code == 200:
                            status_data = status_response.json()
                            
                            if status_data["status"] == "succeeded":
                                video_url = status_data["output"]
                                
                                # Download video
                                video_response = await client.get(video_url)
                                if video_response.status_code == 200:
                                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                    filename = f"video_{timestamp}_{random.randint(1000, 9999)}.mp4"
                                    filepath = os.path.join(self.media_path, "videos", filename)
                                    
                                    with open(filepath, "wb") as f:
                                        f.write(video_response.content)
                                    
                                    # Save to database
                                    await self._save_generated_content(
                                        content_type="video",
                                        file_path=filepath,
                                        prompt_used=prompt,
                                        generation_model="replicate-video"
                                    )
                                    
                                    logger.info(f"Video generated successfully: {filepath}")
                                    return filepath
                            
                            elif status_data["status"] == "failed":
                                logger.error("Video generation failed")
                                break
                    
                    logger.error("Video generation timed out")
                    return None
                else:
                    logger.error(f"Replicate API error: {response.status_code}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error generating video with Replicate: {e}")
            return None
    
    async def generate_preview_image(self) -> Optional[str]:
        """Generate a preview image for non-subscribers"""
        try:
            preview_prompts = [
                "beautiful woman silhouette, artistic lighting, mysterious, elegant",
                "glamour photography, soft focus, professional model, teaser shot",
                "artistic portrait, partial view, sophisticated, high quality",
                "fashion photography, elegant pose, studio lighting, preview style"
            ]
            
            prompt = random.choice(preview_prompts)
            
            # Try local generation first, then API
            image_path = await self.generate_image_local(prompt, "artistic")
            if not image_path:
                image_path = await self.generate_image_huggingface(prompt, "artistic")
            
            if image_path:
                # Create preview version (blurred/watermarked)
                preview_path = await self._create_preview_version(image_path)
                return preview_path
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating preview image: {e}")
            return None
    
    async def _create_preview_version(self, original_path: str) -> str:
        """Create a preview version of an image (blurred/watermarked)"""
        try:
            # Open original image
            with Image.open(original_path) as img:
                # Apply blur effect
                blurred = img.filter(ImageFilter.GaussianBlur(radius=3))
                
                # Reduce brightness slightly
                enhancer = ImageEnhance.Brightness(blurred)
                preview_img = enhancer.enhance(0.7)
                
                # Save preview
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"preview_{timestamp}_{random.randint(1000, 9999)}.jpg"
                preview_path = os.path.join(self.media_path, "previews", filename)
                
                preview_img.save(preview_path, quality=85)
                
                return preview_path
                
        except Exception as e:
            logger.error(f"Error creating preview version: {e}")
            return original_path
    
    async def _post_process_image(self, image_path: str) -> str:
        """Post-process generated image"""
        try:
            with Image.open(image_path) as img:
                # Enhance image quality
                enhancer = ImageEnhance.Sharpness(img)
                enhanced = enhancer.enhance(1.2)
                
                enhancer = ImageEnhance.Color(enhanced)
                enhanced = enhancer.enhance(1.1)
                
                # Save enhanced version
                enhanced.save(image_path, quality=95, optimize=True)
                
                return image_path
                
        except Exception as e:
            logger.error(f"Error post-processing image: {e}")
            return image_path
    
    def _enhance_prompt(self, prompt: str, style: str) -> str:
        """Enhance prompt based on style and quality keywords"""
        quality_keywords = [
            "high quality", "detailed", "professional", "4k resolution",
            "perfect lighting", "masterpiece", "best quality"
        ]
        
        style_keywords = {
            "artistic": ["artistic", "elegant", "sophisticated", "refined"],
            "fantasy": ["magical", "ethereal", "mystical", "enchanted"],
            "vintage": ["vintage", "retro", "classic", "timeless"]
        }
        
        # Add style-specific keywords
        if style in style_keywords:
            prompt += f", {random.choice(style_keywords[style])}"
        
        # Add quality keywords
        prompt += f", {random.choice(quality_keywords)}"
        
        # Add negative prompt elements
        negative_elements = [
            "low quality", "blurry", "distorted", "ugly", "bad anatomy",
            "worst quality", "low resolution", "pixelated"
        ]
        
        enhanced_prompt = f"{prompt}, negative prompt: {', '.join(negative_elements[:3])}"
        
        return enhanced_prompt
    
    async def _save_generated_content(self, content_type: str, file_path: str, 
                                    prompt_used: str, generation_model: str):
        """Save generated content info to database"""
        try:
            db = next(get_db())
            
            # Get file info
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            
            content = GeneratedContent(
                content_type=content_type,
                file_path=file_path,
                prompt_used=prompt_used,
                generation_model=generation_model,
                file_size=file_size,
                is_posted=False
            )
            
            db.add(content)
            db.commit()
            db.close()
            
        except Exception as e:
            logger.error(f"Error saving content to database: {e}")
    
    async def generate_daily_content(self) -> List[str]:
        """Generate daily content batch"""
        generated_files = []
        
        try:
            # Generate 3-5 images with different styles
            styles = ["artistic", "fantasy", "vintage"]
            
            for style in styles:
                prompts = self.image_prompts.get(style, self.image_prompts["artistic"])
                prompt = random.choice(prompts)
                
                # Try local first, then API
                image_path = await self.generate_image_local(prompt, style)
                if not image_path:
                    image_path = await self.generate_image_huggingface(prompt, style)
                
                if image_path:
                    generated_files.append(image_path)
                
                # Small delay between generations
                await asyncio.sleep(2)
            
            # Generate 1 video if possible
            if self.replicate_token:
                video_prompt = random.choice(self.video_prompts)
                video_path = await self.generate_video_replicate(video_prompt)
                if video_path:
                    generated_files.append(video_path)
            
            logger.info(f"Generated {len(generated_files)} content items")
            return generated_files
            
        except Exception as e:
            logger.error(f"Error generating daily content: {e}")
            return generated_files
    
    async def generate_custom_content(self, user_request: str, content_type: str = "image") -> Optional[str]:
        """Generate custom content based on user request"""
        try:
            # Sanitize and enhance user request
            safe_prompt = self._sanitize_prompt(user_request)
            
            if content_type == "image":
                # Try local first, then API
                image_path = await self.generate_image_local(safe_prompt, "artistic")
                if not image_path:
                    image_path = await self.generate_image_huggingface(safe_prompt, "artistic")
                return image_path
            
            elif content_type == "video" and self.replicate_token:
                return await self.generate_video_replicate(safe_prompt)
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating custom content: {e}")
            return None
    
    def _sanitize_prompt(self, prompt: str) -> str:
        """Sanitize user prompt for safe generation"""
        # Remove potentially harmful or inappropriate content
        forbidden_words = ["child", "minor", "young", "kid", "illegal", "violence"]
        
        words = prompt.lower().split()
        safe_words = [word for word in words if word not in forbidden_words]
        
        safe_prompt = " ".join(safe_words)
        
        # Add safety keywords
        safe_prompt += ", adult, mature, 18+, safe content"
        
        return safe_prompt
